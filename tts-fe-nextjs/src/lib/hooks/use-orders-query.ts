'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';
import {
  getOrders,
  getOrder,
  getOrderByTikTokId,
  synchronizeOrders,
  synchronizeOrdersMultiShop,
  synchronizeOrderDetails,
  getOrderStatistics,
  exportOrders,
  updateOrderTracking,
  getOrderFulfillmentOptions,
  createPackage,
  getShippingLabel
} from '@/lib/api/services/order-service';
import {
  OrderQueryDto,
  OrderSyncDto,
  OrderSyncMultiShopDto,
  CreatePackageDto,
  ShippingLabelResponseDto
} from '@/types/order';
import { createQueryKeys } from '@/lib/api/api-client';

// Create query keys for orders
export const orderKeys = createQueryKeys('orders');

/**
 * Hook for fetching orders with pagination and filtering
 */
export const useOrders = (filters: OrderQueryDto = {}) => {
  const { data: session, status } = useSession();
  const isAuthenticated = status === 'authenticated';

  return useQuery({
    queryKey: orderKeys.list(filters),
    queryFn: () => getOrders(filters, session?.backendToken),
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for fetching a single order by ID
 */
export const useOrder = (id: number) => {
  const { data: session, status } = useSession();
  const isAuthenticated = status === 'authenticated';

  return useQuery({
    queryKey: orderKeys.detail(id),
    queryFn: () => getOrder(id, session?.backendToken),
    enabled: isAuthenticated && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for fetching a single order by TikTok order ID
 */
export const useOrderByTikTokId = (idTT: string) => {
  const { data: session, status } = useSession();
  const isAuthenticated = status === 'authenticated';

  return useQuery({
    queryKey: orderKeys.detail(`tiktok-${idTT}`),
    queryFn: () => getOrderByTikTokId(idTT, session?.backendToken),
    enabled: isAuthenticated && !!idTT,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for fetching order statistics
 */
export const useOrderStatistics = (tiktokShopId: number) => {
  const { data: session, status } = useSession();
  const isAuthenticated = status === 'authenticated';

  return useQuery({
    queryKey: orderKeys.detail(`statistics-${tiktokShopId}`),
    queryFn: () => getOrderStatistics(tiktokShopId, session?.backendToken),
    enabled: isAuthenticated && !!tiktokShopId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook for fetching order fulfillment options
 */
export const useOrderFulfillmentOptions = (tiktokShopId: number) => {
  const { data: session, status } = useSession();
  const isAuthenticated = status === 'authenticated';

  return useQuery({
    queryKey: orderKeys.detail(`fulfillment-options-${tiktokShopId}`),
    queryFn: () => getOrderFulfillmentOptions(tiktokShopId, session?.backendToken),
    enabled: isAuthenticated && !!tiktokShopId,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

/**
 * Hook for synchronizing orders from TikTok Shop
 */
export const useSynchronizeOrders = () => {
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: OrderSyncDto) =>
      synchronizeOrders(data, session?.backendToken),
    onSuccess: (result, variables) => {
      toast.success(
        `Order synchronization started for ${result.shopName}! Job ID: ${result.jobId}`
      );

      // Invalidate orders queries to refresh the data
      queryClient.invalidateQueries({
        queryKey: orderKeys.lists()
      });

      // Invalidate statistics for the specific shop
      queryClient.invalidateQueries({
        queryKey: orderKeys.detail(`statistics-${variables.tiktokShopId}`)
      });
    },
    onError: (error: any) => {
      console.error('Failed to synchronize orders:', error);
      // Error toast is already shown by API client interceptor
    },
  });
};

/**
 * Hook for synchronizing orders from multiple TikTok Shops
 */
export const useSynchronizeOrdersMultiShop = () => {
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: OrderSyncMultiShopDto) =>
      synchronizeOrdersMultiShop(data, session?.backendToken),
    onSuccess: (result, variables) => {
      const totalJobs = result.length;
      const shopNames = result.map(job => job.shopName).join(', ');

      toast.success(
        `Multi-shop synchronization started! ${totalJobs} jobs queued for shops: ${shopNames}`
      );

      // Invalidate orders queries to refresh the data
      queryClient.invalidateQueries({
        queryKey: orderKeys.lists()
      });

      // Invalidate statistics for all shops
      variables.tiktokShopIds.forEach(shopId => {
        queryClient.invalidateQueries({
          queryKey: orderKeys.detail(`statistics-${shopId}`)
        });
      });
    },
    onError: (error: any) => {
      console.error('Failed to synchronize orders from multiple shops:', error);
      // Error toast is already shown by API client interceptor
    },
  });
};

/**
 * Hook for synchronizing order details from TikTok Shop
 */
export const useSynchronizeOrderDetails = () => {
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { tiktokShopId: number; orderIds: string[] }) => 
      synchronizeOrderDetails(data, session?.backendToken),
    onSuccess: (result, variables) => {
      toast.success(
        `Order details synchronized successfully! Updated: ${result.updated}`
      );
      
      // Invalidate orders queries to refresh the data
      queryClient.invalidateQueries({ 
        queryKey: orderKeys.lists() 
      });
      
      // Invalidate specific order details
      variables.orderIds.forEach(orderId => {
        queryClient.invalidateQueries({ 
          queryKey: orderKeys.detail(`tiktok-${orderId}`)
        });
      });
    },
    onError: (error: any) => {
      console.error('Failed to synchronize order details:', error);
      // Error toast is already shown by API client interceptor
    },
  });
};

/**
 * Hook for updating order tracking information
 */
export const useUpdateOrderTracking = () => {
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      id, 
      data 
    }: { 
      id: number; 
      data: {
        trackingNumber?: string;
        shippingProvider?: string;
        shippingProviderId?: string;
      }
    }) => updateOrderTracking(id, data, session?.backendToken),
    onSuccess: (updatedOrder) => {
      toast.success('Order tracking updated successfully!');
      
      // Update the specific order in cache
      queryClient.setQueryData(
        orderKeys.detail(updatedOrder.id),
        updatedOrder
      );
      
      // Invalidate orders lists to refresh
      queryClient.invalidateQueries({ 
        queryKey: orderKeys.lists() 
      });
    },
    onError: (error: any) => {
      console.error('Failed to update order tracking:', error);
      // Error toast is already shown by API client interceptor
    },
  });
};

/**
 * Hook for exporting orders to CSV
 */
export const useExportOrders = () => {
  const { data: session } = useSession();

  return useMutation({
    mutationFn: (filters?: OrderQueryDto) =>
      exportOrders(filters, session?.backendToken),
    onSuccess: (blob) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `orders-export-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Orders exported successfully!');
    },
    onError: (error: any) => {
      console.error('Failed to export orders:', error);
      // Error toast is already shown by API client interceptor
    },
  });
};

/**
 * Hook for creating a package for an order
 */
export const useCreatePackage = () => {
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreatePackageDto) =>
      createPackage(data, session?.backendToken),
    onSuccess: (result, variables) => {
      toast.success('Package created successfully!');

      // Update the specific order in cache
      queryClient.setQueryData(
        orderKeys.detail(variables.orderId),
        (oldData: any) => {
          if (oldData) {
            // Update the order with any new package information if needed
            return { ...oldData };
          }
          return oldData;
        }
      );

      // Invalidate orders lists to refresh
      queryClient.invalidateQueries({
        queryKey: orderKeys.lists()
      });

      // Invalidate the specific order to refresh its data
      queryClient.invalidateQueries({
        queryKey: orderKeys.detail(variables.orderId)
      });
    },
    onError: (error: any) => {
      console.error('Failed to create package:', error);
      // Error toast is already shown by API client interceptor
    },
  });
};

/**
 * Hook for getting shipping label for an order
 */
export const useGetShippingLabel = () => {
  const { data: session } = useSession();

  return useMutation({
    mutationFn: (orderId: number) =>
      getShippingLabel(orderId, session?.backendToken),
    onSuccess: (result: ShippingLabelResponseDto) => {
      if (result.docUrl) {
        // Open the shipping label document in a new tab
        window.open(result.docUrl, '_blank');
        toast.success('Shipping label opened successfully!');
      } else {
        toast.error('No shipping label document URL available');
      }
    },
    onError: (error: any) => {
      console.error('Failed to get shipping label:', error);
      // Error toast is already shown by API client interceptor
    },
  });
};
